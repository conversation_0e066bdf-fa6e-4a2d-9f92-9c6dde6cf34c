@import "tailwindcss";

:root {
  /* 🌿 HSSL Warm Forest Color System */
  /* Cream background with deep forest green text and layered green accents */
  --background: #f4efe1;
  --foreground: #214d3a;

  /* Custom Cream Color */
  --cream: #f4efe1;

  /* Primary Brand Colors - Deep Green (Main Actions) */
  --primary: #2a5e44;
  --primary-foreground: #f4efe1;
  --primary-50: #f0f7f3;
  --primary-100: #dbeee3;
  --primary-200: #b8dcc8;
  --primary-300: #8bc4a4;
  --primary-400: #5ea67d;
  --primary-500: #3f8b60;
  --primary-600: #2a5e44;
  --primary-700: #214d3a;
  --primary-800: #1c3f30;
  --primary-900: #173327;

  /* Secondary Brand Colors - Mid Green (Supporting Actions) */
  --secondary: #639a73;
  --secondary-foreground: #f4efe1;
  --secondary-50: #f4f9f5;
  --secondary-100: #e6f2ea;
  --secondary-200: #cee5d6;
  --secondary-300: #a8d1b7;
  --secondary-400: #7db591;
  --secondary-500: #639a73;
  --secondary-600: #4f7d5c;
  --secondary-700: #42654b;
  --secondary-800: #37523e;
  --secondary-900: #2f4434;

  /* Success Colors - Light Mint Green */
  --success: #aecea9;
  --success-50: #f8fbf7;
  --success-100: #f0f6ef;
  --success-200: #e1edde;
  --success-300: #ccdfc6;
  --success-400: #aecea9;
  --success-500: #8fb888;
  --success-600: #739f6b;
  --success-700: #5d8356;
  --success-800: #4d6b47;
  --success-900: #41593d;

  /* Warning Colors - Warm Amber (complementary to forest theme) */
  --warning: #d97706;
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors - Muted Rust (complementary to forest theme) */
  --error: #dc2626;
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Accent Colors - Pale Green Tint (Layer 4) */
  --accent: #d1e0cd;
  --accent-foreground: #214d3a;
  --accent-50: #f9faf8;
  --accent-100: #f3f5f1;
  --accent-200: #e7ebe3;
  --accent-300: #d1e0cd;
  --accent-400: #b8d0b1;
  --accent-500: #9bbf92;
  --accent-600: #7da873;
  --accent-700: #639058;
  --accent-800: #4f7347;
  --accent-900: #42603c;

  /* Neutral Colors - Warm Cream-based Grays */
  --neutral: #78716c;
  --neutral-50: #faf9f7;
  --neutral-100: #f5f4f1;
  --neutral-200: #ebe8e3;
  --neutral-300: #ddd8d1;
  --neutral-400: #b8b0a7;
  --neutral-500: #8b8078;
  --neutral-600: #78716c;
  --neutral-700: #5d564f;
  --neutral-800: #4a433d;
  --neutral-900: #3d362f;

  /* Gray Scale - Cream-based Warm Tones */
  --gray-50: #faf9f7;
  --gray-100: #f5f4f1;
  --gray-200: #ebe8e3;
  --gray-300: #ddd8d1;
  --gray-400: #b8b0a7;
  --gray-500: #8b8078;
  --gray-600: #78716c;
  --gray-700: #5d564f;
  --gray-800: #4a433d;
  --gray-900: #3d362f;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Typography Scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */

  /* Border Radius */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}



html {
  background-color: #f4efe1;
}

body {
  background: #f4efe1;
  background-color: #f4efe1;
  color: #214d3a;
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* Typography Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Custom Background Utilities */
.bg-cream {
  background-color: #f4efe1;
}

.text-forest {
  color: #214d3a;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-600);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-700);
}

/* 🎨 Enhanced Focus Styles */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px #2a5e44, 0 0 0 4px rgba(42, 94, 68, 0.2);
}

.focus-ring-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 2px #639a73, 0 0 0 4px rgba(99, 154, 115, 0.2);
}

.focus-ring-success:focus {
  outline: none;
  box-shadow: 0 0 0 2px #aecea9, 0 0 0 4px rgba(174, 206, 169, 0.2);
}

.focus-ring-warning:focus {
  outline: none;
  box-shadow: 0 0 0 2px #d97706, 0 0 0 4px rgba(217, 119, 6, 0.2);
}

.focus-ring-error:focus {
  outline: none;
  box-shadow: 0 0 0 2px #dc2626, 0 0 0 4px rgba(220, 38, 38, 0.2);
}

/* 🌈 Gradient Utilities */
.gradient-hero-primary {
  background: linear-gradient(135deg, #2a5e44 0%, #639a73 50%, #aecea9 100%);
}

.gradient-hero-secondary {
  background: linear-gradient(135deg, #639a73 0%, #aecea9 50%, #d1e0cd 100%);
}

.gradient-card-subtle {
  background: linear-gradient(135deg, #f8fbf7 0%, #f4efe1 50%, #f0f6ef 100%);
}

.gradient-interactive-primary {
  background: linear-gradient(135deg, #2a5e44 0%, #639a73 100%);
}

.gradient-interactive-hover {
  background: linear-gradient(135deg, #214d3a 0%, #4f7d5c 100%);
}

/* 🃏 Card Component Styles - Forest Theme */
.card-default {
  background-color: #f4efe1;
  border: 1px solid #cee5d6;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: all 0.2s ease;
}

.card-default:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  border-color: #a8d1b7;
}

.card-gradient {
  background: linear-gradient(to bottom right, #f4efe1, #f0f6ef);
  border: 1px solid #e1edde;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: all 0.2s ease;
}

.card-gradient:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  border-color: #ccdfc6;
}

.card-elevated {
  background-color: #f4efe1;
  border: 1px solid #cee5d6;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transition: all 0.2s ease;
}

.card-elevated:hover {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  border-color: #a8d1b7;
}

.card-forest-light {
  background-color: #f0f6ef;
  border: 1px solid #e1edde;
  color: #214d3a;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.card-forest-light:hover {
  background-color: #e1edde;
  border-color: #ccdfc6;
}

.card-forest-mint {
  background-color: #f8fbf7;
  border: 1px solid #ccdfc6;
  color: #214d3a;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.card-forest-mint:hover {
  background-color: #f0f6ef;
  border-color: #aecea9;
}

/* 🔘 Button Component Styles - Forest Theme */
.btn-primary {
  background-color: #2a5e44;
  color: #ffffff;
  border: 1px solid #2a5e44;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: #214d3a;
  border-color: #214d3a;
}

.btn-primary:active {
  background-color: #1c3f30;
}

.btn-primary:focus {
  box-shadow: 0 0 0 2px #2a5e44, 0 0 0 4px rgba(42, 94, 68, 0.2);
}

.btn-primary:disabled {
  background-color: #8bc4a4;
  border-color: #8bc4a4;
}

.btn-secondary {
  background-color: #e6f2ea;
  color: #214d3a;
  border: 1px solid #cee5d6;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: #cee5d6;
  border-color: #a8d1b7;
}

.btn-secondary:active {
  background-color: #a8d1b7;
}

.btn-secondary:focus {
  box-shadow: 0 0 0 2px #14b8a6, 0 0 0 4px rgba(20, 184, 166, 0.2);
}

.btn-secondary:disabled {
  background-color: #f0fdfa;
  color: #5eead4;
}

.btn-outline {
  background-color: transparent;
  color: #059669;
  border: 1px solid #059669;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  background-color: #ecfdf5;
  border-color: #047857;
  color: #047857;
}

.btn-outline:active {
  background-color: #d1fae5;
}

.btn-outline:focus {
  box-shadow: 0 0 0 2px #10b981, 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.btn-outline:disabled {
  color: #6ee7b7;
  border-color: #6ee7b7;
}

.btn-ghost {
  background-color: transparent;
  color: #059669;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.btn-ghost:hover {
  background-color: #ecfdf5;
  color: #047857;
}

.btn-ghost:active {
  background-color: #d1fae5;
}

.btn-ghost:focus {
  box-shadow: 0 0 0 2px #10b981, 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.btn-ghost:disabled {
  color: #6ee7b7;
}

/* 📱 Responsive Design Utilities */
.section-padding {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.section-padding-lg {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

@media (min-width: 1024px) {
  .section-padding-lg {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

.container-responsive {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

/* Optimized animations using transform and opacity only */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Hardware acceleration for smooth animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #2a5e44;
  outline-offset: 2px;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}
